#[glue::contract]
mod vcloud_db {
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;

    // Include module implementations
    include!("user_service.rs");
    include!("order.rs");
    include!("cli_version.rs");
    include!("currency.rs");
    include!("service_category.rs");

    // User Service data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct UserService {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(default)]
        pub duration: i64,
        #[serde(default)]
        pub amount: f64,
        #[serde(rename = "publicKey", default)]
        pub public_key: String,
        #[serde(default)]
        pub provider: String,
        #[serde(rename = "providerAddress", default)]
        pub provider_address: String,
        #[serde(default)]
        pub address: String,
        #[serde(rename = "serviceID", default)]
        pub service_id: String,
        #[serde(rename = "serviceActivated", default)]
        pub service_activated: bool,
        #[serde(default)]
        pub status: String,  // Changed from ServiceStatus enum to String
        #[serde(rename = "serviceOptions", default)]
        pub service_options: HashMap<String, String>,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        // New fields added
        #[serde(rename = "endAt", default)]
        pub end_at: i64,
        #[serde(rename = "serviceActivateTS", default)]
        pub service_activate_ts: i64,
        #[serde(rename = "serviceRunningTS", default)]
        pub service_running_ts: i64,
        #[serde(rename = "serviceAbortTS", default)]
        pub service_abort_ts: i64,
        #[serde(rename = "serviceDoneTS", default)]
        pub service_done_ts: i64,
        #[serde(rename = "serviceRefundTS", default)]
        pub service_refund_ts: i64,
        #[serde(default)]
        pub service: String,
        #[serde(rename = "createdAddr", default)]
        pub created_addr: String,
        #[serde(rename = "labelHash", default)]
        pub label_hash: String,
    }

    // Order data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct SingleServiceBriefInfo {
        #[serde(rename = "userServiceID", default)]
        pub user_service_id: String,
        #[serde(rename = "duration", default)]
        pub duration: i64,
        #[serde(rename = "amount", default)]
        pub amount: f64,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct Order {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        #[serde(rename = "type", default)]
        pub order_type: String,
        #[serde(rename = "amount", default)]
        pub amount: f64,
        #[serde(rename = "amountPaid", default)]
        pub amount_paid: f64,
        #[serde(rename = "provider", default)]
        pub provider: String,
        #[serde(rename = "address", default)]
        pub address: String,
        #[serde(rename = "recipient", default)]
        pub recipient: String,
        #[serde(rename = "status", default)]
        pub status: String,
        #[serde(rename = "lastPaymentTS", default)]
        pub last_payment_ts: i64,
        #[serde(rename = "paidTS", default)]
        pub paid_ts: i64,
        #[serde(rename = "filedTS", default)]
        pub filed_ts: i64,
        #[serde(rename = "publicKey", default)]
        pub public_key: String,
        #[serde(rename = "userServiceIDs", default)]
        pub user_service_ids: Vec<String>,
        #[serde(rename = "items", default)]
        pub items: Vec<SingleServiceBriefInfo>,
    }

    // CLI Version data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct CliVersion {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(default)]
        pub version: String,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        #[serde(rename = "changeLog", default)]
        pub change_log: String,
        #[serde(rename = "minimalSupported", default)]
        pub minimal_supported: String,
    }

    // Currency data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct Currency {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        #[serde(rename = "nameOrId", default)]
        pub name_or_id: String,
        #[serde(rename = "contractId", default)]
        pub contract_id: String,
        #[serde(rename = "symbolName", default)]
        pub symbol_name: String,
        #[serde(rename = "contractType", default)]
        pub contract_type: String,
        #[serde(default)]
        pub unit: i32,
        #[serde(rename = "exchangeRate", default)]
        pub exchange_rate: f64,
    }

    // Service Category data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct ServiceCategory {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        #[serde(default)]
        pub provider: String,
        #[serde(default)]
        pub name: String,
        #[serde(rename = "serviceOptions", default)]
        pub service_options: HashMap<String, Vec<String>>,
        #[serde(default)]
        pub description: String,
        #[serde(rename = "name2ID", default)]
        pub name2_id: HashMap<String, String>,
        #[serde(rename = "apiHost", default)]
        pub api_host: String,
    }

    /// Query parameters for filtering user services
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceQueryParams {
        #[serde(rename = "serviceID")]
        pub service_id: Option<String>,
        pub address: Option<String>,
        pub provider: Option<String>,
        #[serde(rename = "providerAddress")]
        pub provider_address: Option<String>,  // New field for provider_address filtering
        pub status: Option<String>,  // Changed from ServiceStatus to String
        #[serde(rename = "serviceActivated")]
        pub service_activated: Option<bool>,  // New field for service_activated filtering
        pub ids: Option<Vec<String>>,  // New field for batch ID queries
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering orders
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderQueryParam {
        pub ids: Option<Vec<String>>,
        #[serde(rename = "serviceID")]
        pub service_id: Option<String>,
        #[serde(rename = "service")]
        pub service: Option<String>,
        #[serde(rename = "address")]
        pub address: Option<String>,
        #[serde(rename = "recipient")]
        pub recipient: Option<String>,
        #[serde(rename = "type")]
        pub order_type: Option<String>,
        #[serde(rename = "statuses")]
        pub statuses: Option<Vec<String>>,
        #[serde(rename = "tsStart")]
        pub ts_start: Option<i64>,
        #[serde(rename = "tsEnd")]
        pub ts_end: Option<i64>,
        #[serde(rename = "limit")]
        pub limit: Option<u64>,
        #[serde(rename = "offset")]
        pub offset: Option<u64>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering CLI versions
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CliVersionQueryParams {
        pub version: Option<String>,
        #[serde(rename = "minimalSupported")]
        pub minimal_supported: Option<String>,
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering currencies
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CurrencyQueryParams {
        pub ids: Option<Vec<String>>,
        #[serde(rename = "nameOrId")]
        pub name_or_id: Option<String>,
        #[serde(rename = "contractId")]
        pub contract_id: Option<String>,
        #[serde(rename = "symbolName")]
        pub symbol_name: Option<String>,
        #[serde(rename = "contractType")]
        pub contract_type: Option<String>,
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering service categories
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct ServiceCategoryQueryParams {
        pub ids: Option<Vec<String>>,
        pub provider: Option<String>,
        pub name: Option<String>,
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    // /// Distinct parameters for order queries
    // #[derive(Debug, Serialize, Deserialize, Clone)]
    // pub struct OrderDistinctParam {
    //     #[serde(rename = "distinctField", default)]
    //     pub distinct_field: String,
    //     #[serde(rename = "address", default)]
    //     pub address: String,
    //     #[serde(rename = "statuses", default)]
    //     pub statuses: Vec<String>,
    // }

    /// Batch operation result
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct BatchResult {
        pub created: u64,
        pub updated: u64,
        pub deleted: u64,
        pub errors: Vec<String>,
    }



    /// User service update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceUpdate {
        pub filter: UserServiceQueryParams,
        pub update_data: serde_json::Value,
    }

    /// Order update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderUpdate {
        pub filter: OrderQueryParam,
        pub update_data: serde_json::Value,
    }

    /// CLI version update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CliVersionUpdate {
        pub filter: CliVersionQueryParams,
        pub update_data: serde_json::Value,
    }

    /// User service bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<UserServiceQueryParams>,
        pub data: Option<serde_json::Value>,
    }

    /// Order bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<OrderQueryParam>,
        pub data: Option<serde_json::Value>,
    }

    /// CLI version bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CliVersionBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<CliVersionQueryParams>,
        pub data: Option<serde_json::Value>,
    }

    /// Currency update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CurrencyUpdate {
        pub filter: CurrencyQueryParams,
        pub update_data: serde_json::Value,
    }

    /// Currency bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct CurrencyBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<CurrencyQueryParams>,
        pub data: Option<serde_json::Value>,
    }

    /// Service category update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct ServiceCategoryUpdate {
        pub filter: ServiceCategoryQueryParams,
        pub update_data: serde_json::Value,
    }

    /// Service category bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct ServiceCategoryBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<ServiceCategoryQueryParams>,
        pub data: Option<serde_json::Value>,
    }

    #[glue::storage]
    pub struct VCloudDB {
        pub user_services: glue::collections::Map<String, UserService>,
        pub orders: glue::collections::Map<String, Order>,
        pub cli_versions: glue::collections::Map<String, CliVersion>,
        pub currencies: glue::collections::Map<String, Currency>,
        pub service_categories: glue::collections::Map<String, ServiceCategory>,
    }

    impl VCloudDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // User Service indexes
            // Index by provider for efficient provider-based queries
            self.user_services.bind_index(
                "user_services_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by address for efficient address-based queries
            self.user_services.bind_index(
                "user_services_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by provider_address for efficient provider_address-based queries
            self.user_services.bind_index(
                "user_services_provider_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider_address, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.user_services.bind_index(
                "user_services_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by service_id for efficient service_id-based queries
            self.user_services.bind_index(
                "user_services_service_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_id, v.created_at)]
                }),
            );

            // Index by service_activated for efficient service_activated-based queries
            self.user_services.bind_index(
                "user_services_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_activated, v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );

            // Composite index: provider + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_provider_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.provider, v.status, v.created_at)]
                }),
            );

            // Composite index: address + service_activated for IDs + address + service_activated queries
            self.user_services.bind_index(
                "user_services_address_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.service_activated, v.created_at)]
                }),
            );

            // Index by created_at for time-based queries
            self.user_services.bind_index(
                "user_services_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by updated_at for time-based queries
            self.user_services.bind_index(
                "user_services_updated_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.updated_at)]
                }),
            );

            // Order indexes
            // Index by address for efficient address-based queries
            self.orders.bind_index(
                "order_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by recipient for efficient recipient-based queries
            self.orders.bind_index(
                "order_recipient",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.recipient, v.created_at)]
                }),
            );

            // Index by provider for efficient provider-based queries
            self.orders.bind_index(
                "order_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.orders.bind_index(
                "order_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by order_type for efficient type-based queries
            self.orders.bind_index(
                "order_type",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.order_type, v.created_at)]
                }),
            );

            // Index by paid_ts for time-based queries
            self.orders.bind_index(
                "order_paid_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.paid_ts)]
                }),
            );

            // Index by filed_ts for time-based queries
            self.orders.bind_index(
                "order_filed_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.filed_ts)]
                }),
            );

            // Index by created_at for time-based queries
            self.orders.bind_index(
                "order_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.orders.bind_index(
                "order_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );

            // CLI Version indexes
            // Index by created_at for time-based queries
            self.cli_versions.bind_index(
                "cli_versions_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by version for efficient version-based queries
            self.cli_versions.bind_index(
                "cli_versions_version",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.version, v.created_at)]
                }),
            );

            // Currency indexes
            // Index by created_at for time-based queries
            self.currencies.bind_index(
                "currencies_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by nameOrId for efficient nameOrId-based queries
            self.currencies.bind_index(
                "currencies_name_or_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.name_or_id, v.created_at)]
                }),
            );

            // Index by symbolName for efficient symbolName-based queries
            self.currencies.bind_index(
                "currencies_symbol_name",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.symbol_name, v.created_at)]
                }),
            );

            // Index by contractId for efficient contractId-based queries
            self.currencies.bind_index(
                "currencies_contract_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.contract_id, v.created_at)]
                }),
            );

            // Service Category indexes
            // Index by created_at for time-based queries
            self.service_categories.bind_index(
                "service_categories_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by provider for efficient provider-based queries
            self.service_categories.bind_index(
                "service_categories_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by name for efficient name-based queries
            self.service_categories.bind_index(
                "service_categories_name",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.name, v.created_at)]
                }),
            );

        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                user_services: glue::collections::Map::new(),
                orders: glue::collections::Map::new(),
                cli_versions: glue::collections::Map::new(),
                currencies: glue::collections::Map::new(),
                service_categories: glue::collections::Map::new(),
            };
            ret.bind_index();
            ret
        }

        /// Get current timestamp - returns actual Unix timestamp
        fn get_current_timestamp(&self) -> i64 {
            // Get current Unix timestamp in seconds
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as i64
        }










































        // /// Get distinct values for a specific field in orders
        // pub fn distinct_orders(&self, params_json: String) -> anyhow::Result<String> {
        //     let params: OrderDistinctParam = serde_json::from_str(&params_json)?;

        //     let mut distinct_values = std::collections::HashSet::new();

        //     // Iterate through all orders
        //     let mut iter = self.orders.index("order_created_at").iter(
        //         false,
        //         &format!("{:0>19}", 0),
        //         &format!("{:9>19}", i64::MAX)
        //     );
        //     while iter.next() {
        //         let order = iter.value()?;

        //         // Skip deleted orders
        //         if order.deleted_at > 0 {
        //             continue;
        //         }

        //         // Apply address filter
        //         if !params.address.is_empty() && order.address != params.address {
        //             continue;
        //         }

        //         // Apply statuses filter
        //         if !params.statuses.is_empty() && !params.statuses.contains(&order.status) {
        //             continue;
        //         }

        //         // Extract the distinct field value
        //         let field_value = match params.distinct_field.as_str() {
        //             "status" => order.status.clone(),
        //             "type" => order.order_type.clone(),
        //             "provider" => order.provider.clone(),
        //             "address" => order.address.clone(),
        //             "recipient" => order.recipient.clone(),
        //             // Support both old and new field names for backward compatibility
        //             "Status" => order.status.clone(),
        //             "Type" => order.order_type.clone(),
        //             "Provider" => order.provider.clone(),
        //             "Address" => order.address.clone(),
        //             "Recipient" => order.recipient.clone(),
        //             _ => continue, // Skip unknown fields
        //         };

        //         distinct_values.insert(field_value);
        //     }

        //     let result: Vec<String> = distinct_values.into_iter().collect();
        //     Ok(serde_json::to_string(&result)?)
        // }

        // /// Update many orders based on filter criteria
        // pub fn update_many_orders(&mut self, filter_json: String, update_json: String) -> anyhow::Result<String> {
        //     let filter_params: OrderQueryParam = serde_json::from_str(&filter_json)
        //         .map_err(|e| anyhow::anyhow!("Failed to parse filter JSON: {}", e))?;
        //     let update_order: Order = serde_json::from_str(&update_json)
        //         .map_err(|e| anyhow::anyhow!("Failed to parse update JSON: {}", e))?;

        //     let mut result = BatchResult {
        //         created: 0,
        //         updated: 0,
        //         deleted: 0,
        //         errors: Vec::new(),
        //     };

        //     // Find orders matching the filter criteria
        //     let mut orders_to_update = Vec::new();

        //     if let Some(ref ids) = filter_params.ids {
        //         for id in ids {
        //             if let Some(order) = self.orders.get(id) {
        //                 if self.matches_order_filters(&order, &filter_params) {
        //                     orders_to_update.push(order);
        //                 }
        //             }
        //         }
        //     } else {
        //         let mut iter = self.orders.index("order_created_at").iter(
        //             false,
        //             &format!("{:0>19}", 0),
        //             &format!("{:9>19}", i64::MAX)
        //         );
        //         while iter.next() {
        //             let order = iter.value()?;
        //             if self.matches_order_filters(&order, &filter_params) {
        //                 orders_to_update.push(order);
        //             }
        //         }
        //     }

        //     // Update each matching order
        //     for mut order in orders_to_update {
        //         // Update only non-empty/valid fields from update_order
        //         if !update_order.order_type.is_empty() {
        //             order.order_type = update_order.order_type.clone();
        //         }
        //         if update_order.amount > 0.0 {
        //             order.amount = update_order.amount;
        //         }
        //         if update_order.amount_paid > 0.0 {
        //             order.amount_paid = update_order.amount_paid;
        //         }
        //         if !update_order.provider.is_empty() {
        //             order.provider = update_order.provider.clone();
        //         }
        //         if !update_order.address.is_empty() {
        //             order.address = update_order.address.clone();
        //         }
        //         if !update_order.recipient.is_empty() {
        //             order.recipient = update_order.recipient.clone();
        //         }
        //         if !update_order.status.is_empty() {
        //             order.status = update_order.status.clone();
        //         }
        //         if update_order.last_payment_ts > 0 {
        //             order.last_payment_ts = update_order.last_payment_ts;
        //         }
        //         if update_order.paid_ts > 0 {
        //             order.paid_ts = update_order.paid_ts;
        //         }
        //         if update_order.filed_ts > 0 {
        //             order.filed_ts = update_order.filed_ts;
        //         }
        //         if !update_order.public_key.is_empty() {
        //             order.public_key = update_order.public_key.clone();
        //         }
        //         if !update_order.user_service_ids.is_empty() {
        //             order.user_service_ids = update_order.user_service_ids.clone();
        //         }
        //         if !update_order.items.is_empty() {
        //             order.items = update_order.items.clone();
        //         }

        //         self.orders.insert(&order._id, &order);
        //         result.updated += 1;
        //     }

        //     Ok(serde_json::to_string(&result)?)
        // }






        // ========== UNIFIED INTERFACE FUNCTIONS ==========

        /// Unified find function for all tables
        /// Replaces query functions for both user_service and order tables
        #[glue::readonly]
        pub fn find(&self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.find_user_service(filter_json_string),
                "order" => self.find_order(filter_json_string),
                "cli_version" => self.find_cli_version(filter_json_string),
                "currency" => self.find_currency(filter_json_string),
                "service_category" => self.find_service_category(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified count function for all tables
        #[glue::readonly]
        pub fn count(&self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.count_user_service(filter_json_string),
                "order" => self.count_order(filter_json_string),
                "cli_version" => self.count_cli_version(filter_json_string),
                "currency" => self.count_currency(filter_json_string),
                "service_category" => self.count_service_category(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified get function for all tables
        /// Retrieves a single record by ID
        #[glue::readonly]
        pub fn get(&self, table_name: String, id: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.get_user_service(id),
                "order" => self.get_order(id),
                "cli_version" => self.get_cli_version(id),
                "currency" => self.get_currency(id),
                "service_category" => self.get_service_category(id),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified update function for all tables
        /// Updates a record with the provided data
        #[glue::atomic]
        pub fn update(&mut self, table_name: String, update_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => {
                    self.update_user_service(update_json_string)?;
                    Ok("{}".to_string())
                },
                "order" => {
                    self.update_order(update_json_string)?;
                    Ok("{}".to_string())
                },
                "cli_version" => {
                    self.update_cli_version(update_json_string)?;
                    Ok("{}".to_string())
                },
                "currency" => self.update_currency(update_json_string),
                "service_category" => {
                    self.update_service_category(update_json_string)?;
                    Ok("{}".to_string())
                },
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified insert function for all tables
        /// Replaces create functions for both user_service and order tables
        #[glue::atomic]
        pub fn insert(&mut self, table_name: String, insert_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.insert_user_service(insert_json_string),
                "order" => self.insert_order(insert_json_string),
                "cli_version" => self.insert_cli_version(insert_json_string),
                "currency" => self.insert_currency(insert_json_string),
                "service_category" => self.insert_service_category(insert_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified insert_many function for all tables
        /// Replaces createMany functions for both user_service and order tables
        #[glue::atomic]
        pub fn insert_many(&mut self, table_name: String, insert_many_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.insert_many_user_service(insert_many_json_string),
                "order" => self.insert_many_order(insert_many_json_string),
                "cli_version" => self.insert_many_cli_version(insert_many_json_string),
                "currency" => self.insert_many_currency(insert_many_json_string),
                "service_category" => self.insert_many_service_category(insert_many_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified update_many function for all tables
        /// Executes partial field updates (only updates non-empty specified fields)
        #[glue::atomic]
        pub fn update_many(&mut self, table_name: String, update_many_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.update_many_user_service(update_many_json_string),
                "order" => self.update_many_order(update_many_json_string),
                "cli_version" => self.update_many_cli_version(update_many_json_string),
                "currency" => self.update_many_currency(update_many_json_string),
                "service_category" => self.update_many_service_category(update_many_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified bulk_write function for all tables
        /// Supports batch operations: delete_many, insert, update
        #[glue::atomic]
        pub fn bulk_write(&mut self, table_name: String, bulk_write_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.bulk_write_user_service(bulk_write_json_string),
                "order" => self.bulk_write_order(bulk_write_json_string),
                "cli_version" => self.bulk_write_cli_version(bulk_write_json_string),
                "currency" => self.bulk_write_currency(bulk_write_json_string),
                "service_category" => self.bulk_write_service_category(bulk_write_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified delete function for all tables
        /// Deletes a single record matching the filter conditions
        #[glue::atomic]
        pub fn delete(&mut self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.delete_user_service(filter_json_string),
                "order" => self.delete_order(filter_json_string),
                "cli_version" => self.delete_cli_version(filter_json_string),
                "currency" => self.delete_currency(filter_json_string),
                "service_category" => self.delete_service_category(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified delete_many function for all tables
        /// Deletes multiple records matching the filter conditions
        #[glue::atomic]
        pub fn delete_many(&mut self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.delete_many_user_service(filter_json_string),
                "order" => self.delete_many_order(filter_json_string),
                "cli_version" => self.delete_many_cli_version(filter_json_string),
                "currency" => self.delete_many_currency(filter_json_string),
                "service_category" => self.delete_many_service_category(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }
    }

    // Include test modules
    #[cfg(test)]
    include!("test_user_service.rs");

    #[cfg(test)]
    include!("test_order.rs");

    #[cfg(test)]
    include!("test_cli_version.rs");

    #[cfg(test)]
    include!("test_currency.rs");

    #[cfg(test)]
    include!("test_service_category.rs");
}