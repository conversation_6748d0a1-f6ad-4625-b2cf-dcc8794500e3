import json
import time
import pytest
from vm import ContractTester

vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    vcloudClient.constructor()


def create_test_user_service(service_id="test_service_1", address="0xtest_address_1", provider="test_provider_1", status="active"):
    """Create a test UserService with realistic data"""
    # unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        # "_id": f"{service_id}_{unique_suffix}",
        "_id": service_id,
        "duration": 3600,
        "amount": 100.0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "provider": provider,
        "providerAddress": "0xprovider_address_123",
        "address": address,
        "serviceID": "service_type_compute",
        "serviceActivated": True,
        "status": status,
        "serviceOptions": {
            "cpu": "4",
            "memory": "8GB",
            "storage": "100GB"
        },
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "endAt": 0,
        "serviceActivateTS": 0,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "compute",
        "createdAddr": address,
        "labelHash": "0x123456789abcdef"
    }


def create_test_order(order_id="test_order_1", address="0xtest_address_1", provider="test_provider_1", status="pending"):
    """Create a test Order with realistic data"""
    # unique_suffix = str(int(time.time() * 1000000))[-6:]
    return {
        # "_id": f"{order_id}_{unique_suffix}",
        "_id": order_id,
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "type": "service_purchase",
        "amount": 500.0,
        "amountPaid": 0.0,
        "provider": provider,
        "address": address,
        "recipient": "0xrecipient_address",
        "status": status,
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
        "userServiceIDs": [],
        "items": []
    }


def create_test_cli_version(version="1.0.0", change_log="Initial release", minimal_supported="1.0.0"):
    """Create a test CLI Version with realistic data"""
    return {
        "_id": version,
        "version": version,
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "changeLog": change_log,
        "minimalSupported": minimal_supported
    }


def create_test_currency(currency_id="btc_001", name_or_id="Bitcoin", symbol_name="BTC", contract_id="0xbtc_contract"):
    """Create a test Currency with realistic data"""
    return {
        "_id": currency_id,
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "nameOrId": name_or_id,
        "contractId": contract_id,
        "symbolName": symbol_name,
        "contractType": "ERC20",
        "unit": 8,
        "exchangeRate": 50000.0
    }


def create_test_order_service(order_service_id="os_001", order_id="order_001", user_service_id="user_service_001", order_status="pending", order_type="compute"):
    """Create a test Order Service with realistic data"""
    return {
        "_id": order_service_id,
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "orderID": order_id,
        "userServiceID": user_service_id,
        "orderStatus": order_status,
        "orderType": order_type
    }


def test_unified_insert_user_service():
    """Test unified insert function for user services"""
    service = create_test_user_service("unified_insert_test")
    service_json = json.dumps(service)
    
    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    assert result == service["_id"]


def test_unified_insert_order():
    """Test unified insert function for orders"""
    order = create_test_order("unified_insert_order_test")
    order_json = json.dumps(order)
    
    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    assert result == order["_id"]


def test_unified_find_user_service():
    """Test unified find function for user services"""
    # Create test service
    service = create_test_user_service("unified_find_test", "0xfind_addr", "find_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    
    # Test unified find
    filter_params = {
        "address": "0xfind_addr",
        "status": "active",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service["_id"] for s in found_services)


def test_unified_find_order():
    """Test unified find function for orders"""
    # Create test order
    order = create_test_order("unified_find_order_test", "0xfind_order_addr", "find_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    
    # Test unified find
    filter_params = {
        "address": "0xfind_order_addr",
        "statuses": ["pending"],
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    
    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    assert any(o["_id"] == order["_id"] for o in found_orders)


def test_unified_count_user_service():
    """Test unified count function for user services"""
    # Create test services
    services = [
        create_test_user_service("count_test_1", "0xcount_addr", "count_provider", "active"),
        create_test_user_service("count_test_2", "0xcount_addr", "count_provider", "active"),
    ]
    
    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None
    
    # Test unified count
    filter_params = {
        "address": "0xcount_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 2


def test_unified_count_order():
    """Test unified count function for orders"""
    # Create test orders
    orders = [
        create_test_order("count_order_test_1", "0xcount_order_addr", "count_order_provider", "pending"),
        create_test_order("count_order_test_2", "0xcount_order_addr", "count_order_provider", "pending"),
    ]
    
    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None
    
    # Test unified count
    filter_params = {
        "address": "0xcount_order_addr",
        "statuses": ["pending"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 2


def test_unified_insert_many_user_service():
    """Test unified insert_many function for user services"""
    services = [
        create_test_user_service("insert_many_test_1", "0xinsert_many_addr", "insert_many_provider", "active"),
        create_test_user_service("insert_many_test_2", "0xinsert_many_addr", "insert_many_provider", "active"),
    ]
    
    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_unified_insert_many_order():
    """Test unified insert_many function for orders"""
    orders = [
        create_test_order("insert_many_order_test_1", "0xinsert_many_order_addr", "insert_many_order_provider", "pending"),
        create_test_order("insert_many_order_test_2", "0xinsert_many_order_addr", "insert_many_order_provider", "pending"),
    ]
    
    orders_json = json.dumps(orders)
    result, err = vcloudClient.execute("insert_many", str, "order", orders_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_unified_delete_user_service():
    """Test unified delete function for user services"""
    # Create test service
    service = create_test_user_service("delete_test", "0xdelete_addr", "delete_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    
    # Test unified delete
    filter_params = {
        "ids": [service["_id"]]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_unified_delete_order():
    """Test unified delete function for orders"""
    # Create test order
    order = create_test_order("delete_order_test", "0xdelete_order_addr", "delete_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    
    # Test unified delete
    filter_params = {
        "_ids": [order["_id"]]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "order", filter_json)
    assert err is None
    
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_unified_update_user_service():
    """Test unified update function for user services"""
    # Create test service
    service = create_test_user_service("update_unified_test", "0xupdate_addr", "update_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Update the service
    service["amount"] = 200.0
    service["status"] = "suspended"
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Verify the update using unified get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 200.0
    assert updated_service["status"] == "suspended"


def test_unified_update_order():
    """Test unified update function for orders"""
    # Create test order
    order = create_test_order("update_unified_order_test", "0xupdate_order_addr", "update_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Update the order
    order["amount"] = 750.0
    order["status"] = "paid"
    order["amountPaid"] = 750.0
    updated_order_json = json.dumps(order)
    result, err = vcloudClient.execute("update", str, "order", updated_order_json)
    assert err is None

    # Verify the update using unified get
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["amount"] == 750.0
    assert updated_order["status"] == "paid"
    assert updated_order["amountPaid"] == 750.0


def test_unified_get_user_service():
    """Test unified get function for user services"""
    # Create test service
    service = create_test_user_service("get_unified_test", "0xget_addr", "get_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == service["_id"]
    assert retrieved_service["amount"] == service["amount"]
    assert retrieved_service["provider"] == service["provider"]

    # Test non-existent service
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", "non_existent_id")
    assert err is not None
    assert "not found" in str(err)


def test_unified_get_order():
    """Test unified get function for orders"""
    # Create test order
    order = create_test_order("get_unified_order_test", "0xget_order_addr", "get_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["_id"] == order["_id"]
    assert retrieved_order["amount"] == order["amount"]
    assert retrieved_order["provider"] == order["provider"]
    assert retrieved_order["status"] == order["status"]

    # Test non-existent order
    result, err = vcloudClient.executeReadOnly("get", str, "order", "non_existent_order_id")
    assert err is not None
    assert "not found" in str(err)


def test_unified_delete_many_user_service():
    """Test unified delete_many function for user services"""
    # Create test services
    services = [
        create_test_user_service("delete_many_test_1", "0xdelete_many_addr", "delete_many_provider", "active"),
        create_test_user_service("delete_many_test_2", "0xdelete_many_addr", "delete_many_provider", "active"),
        create_test_user_service("delete_many_test_3", "0xdelete_many_addr", "delete_many_provider", "inactive"),
    ]

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test unified delete_many with status filter
    filter_params = {
        "address": "0xdelete_many_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "user_service", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 active services


def test_unified_delete_many_order():
    """Test unified delete_many function for orders"""
    # Create test orders
    orders = [
        create_test_order("delete_many_order_test_1", "0xdelete_many_order_addr", "delete_many_order_provider", "pending"),
        create_test_order("delete_many_order_test_2", "0xdelete_many_order_addr", "delete_many_order_provider", "pending"),
        create_test_order("delete_many_order_test_3", "0xdelete_many_order_addr", "delete_many_order_provider", "paid"),
    ]

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Test unified delete_many with status filter
    filter_params = {
        "address": "0xdelete_many_order_addr",
        "statuses": ["pending"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "order", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 pending orders


def test_unified_complex_queries():
    """Test complex queries with multiple filters"""
    # Create test data with variety
    services = [
        create_test_user_service("complex_1", "0xcomplex_addr1", "provider1", "active"),
        create_test_user_service("complex_2", "0xcomplex_addr1", "provider2", "inactive"),
        create_test_user_service("complex_3", "0xcomplex_addr2", "provider1", "active"),
        create_test_user_service("complex_4", "0xcomplex_addr2", "provider2", "suspended"),
    ]

    # Modify amounts for variety
    services[0]["amount"] = 100.0
    services[1]["amount"] = 200.0
    services[2]["amount"] = 300.0
    services[3]["amount"] = 150.0

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test complex query: address + status + provider
    filter_params = {
        "address": "0xcomplex_addr1",
        "status": "active",
        "provider": "provider1",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 1
    assert found_services[0]["address"] == "0xcomplex_addr1"
    assert found_services[0]["status"] == "active"
    assert found_services[0]["provider"] == "provider1"


def test_unified_pagination():
    """Test pagination with unified interface"""
    # Create multiple test services
    services = []
    for i in range(5):
        service = create_test_user_service(f"pagination_test_{i}", "0xpagination_addr", "pagination_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test pagination - first page
    filter_params = {
        "address": "0xpagination_addr",
        "limit": 2,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page1_services = json.loads(result)
    assert len(page1_services) == 2

    # Test pagination - second page
    filter_params["offset"] = 2
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page2_services = json.loads(result)
    assert len(page2_services) == 2

    # Verify no overlap between pages
    page1_ids = {s["_id"] for s in page1_services}
    page2_ids = {s["_id"] for s in page2_services}
    assert len(page1_ids.intersection(page2_ids)) == 0


def test_unified_error_handling():
    """Test error handling in unified interface"""
    # Test invalid JSON
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", "invalid_json")
    assert err is not None

    # Test empty table name
    result, err = vcloudClient.executeReadOnly("find", str, "", "{}")
    assert err is not None

    # Test invalid operation
    result, err = vcloudClient.execute("invalid_operation", str, "user_service", "{}")
    assert err is not None

    # Test get with empty ID
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", "")
    assert err is not None


def test_unified_data_validation():
    """Test data validation in unified interface"""
    # Test creating service with empty ID
    invalid_service = create_test_user_service("validation_test")
    invalid_service["_id"] = ""
    invalid_service_json = json.dumps(invalid_service)
    result, err = vcloudClient.execute("insert", str, "user_service", invalid_service_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)

    # Test creating order with empty ID
    invalid_order = create_test_order("validation_order_test")
    invalid_order["_id"] = ""
    invalid_order_json = json.dumps(invalid_order)
    result, err = vcloudClient.execute("insert", str, "order", invalid_order_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)


def test_unified_lifecycle():
    """Test complete lifecycle using unified interface"""
    # Create service using unified interface
    service = create_test_user_service("lifecycle_unified_test", "0xlifecycle_addr", "lifecycle_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Get service using unified interface
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "active"

    # Update service using unified interface
    retrieved_service["status"] = "suspended"
    retrieved_service["amount"] = 250.0
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Find service using unified interface
    filter_params = {
        "address": "0xlifecycle_addr",
        "status": "suspended"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service_id for s in found_services)

    # Count services using unified interface
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # Delete service using unified interface
    delete_filter = {"ids": [service_id]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "user_service", delete_filter_json)
    assert err is None
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1

    # Verify service is deleted (hard delete)
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is not None
    assert "not found" in str(err)


def test_unsupported_table_name():
    """Test error handling for unsupported table names"""
    # Test with unsupported table name
    result, err = vcloudClient.executeReadOnly("find", str, "unsupported_table", "{}")
    assert err is not None
    assert "Unsupported table name" in str(err)


# ========== EXTENDED TEST COVERAGE ==========
# Adding comprehensive test cases to reach 100+ tests

def test_unified_insert_user_service_edge_cases():
    """Test edge cases for user service insertion"""
    # Test with minimal required fields
    minimal_service = {
        "_id": f"minimal_test_{int(time.time() * 1000000)}",
        "duration": 0,
        "amount": 0.0,
        "publicKey": "",
        "provider": "",
        "providerAddress": "",
        "address": "",
        "serviceID": "",
        "serviceActivated": False,
        "status": "",
        "serviceOptions": {},
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "endAt": 0,
        "serviceActivateTS": 0,
        "serviceRunningTS": 0,
        "serviceAbortTS": 0,
        "serviceDoneTS": 0,
        "serviceRefundTS": 0,
        "service": "",
        "createdAddr": "",
        "labelHash": ""
    }

    service_json = json.dumps(minimal_service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    assert result == minimal_service["_id"]


def test_unified_insert_order_edge_cases():
    """Test edge cases for order insertion"""
    # Test with minimal required fields
    minimal_order = {
        "_id": f"minimal_order_test_{int(time.time() * 1000000)}",
        "createdAt": 0,
        "updatedAt": 0,
        "deletedAt": 0,
        "type": "",
        "amount": 0.0,
        "amountPaid": 0.0,
        "provider": "",
        "address": "",
        "recipient": "",
        "status": "",
        "lastPaymentTS": 0,
        "paidTS": 0,
        "filedTS": 0,
        "publicKey": "",
        "userServiceIDs": [],
        "items": []
    }

    order_json = json.dumps(minimal_order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    assert result == minimal_order["_id"]


def test_unified_find_user_service_by_provider():
    """Test finding user services by provider"""
    # Create services with different providers
    services = [
        create_test_user_service("provider_test_1", "0xprovider_addr", "provider_alpha", "active"),
        create_test_user_service("provider_test_2", "0xprovider_addr", "provider_beta", "active"),
        create_test_user_service("provider_test_3", "0xprovider_addr", "provider_alpha", "inactive"),
    ]

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Find by specific provider
    filter_params = {
        "provider": "provider_alpha",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 2
    for service in found_services:
        if service["_id"] in [s["_id"] for s in services]:
            assert service["provider"] == "provider_alpha"


def test_unified_find_user_service_by_service_activated():
    """Test finding user services by serviceActivated status"""
    # Create services with different activation status
    services = [
        create_test_user_service("activated_test_1", "0xactivated_addr", "activated_provider", "active"),
        create_test_user_service("activated_test_2", "0xactivated_addr", "activated_provider", "active"),
    ]

    services[0]["serviceActivated"] = True
    services[1]["serviceActivated"] = False

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Find activated services
    filter_params = {
        "address": "0xactivated_addr",
        "service_activated": True,
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == services[0]["_id"]:
            assert service["serviceActivated"] == True


def test_unified_find_order_by_recipient():
    """Test finding orders by recipient"""
    # Create orders with different recipients
    orders = [
        create_test_order("recipient_test_1", "0xrecipient_addr", "recipient_provider", "pending"),
        create_test_order("recipient_test_2", "0xrecipient_addr", "recipient_provider", "pending"),
    ]

    orders[0]["recipient"] = "0xrecipient_alpha"
    orders[1]["recipient"] = "0xrecipient_beta"

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Find by specific recipient
    filter_params = {
        "recipient": "0xrecipient_alpha",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    for order in found_orders:
        if order["_id"] == orders[0]["_id"]:
            assert order["recipient"] == "0xrecipient_alpha"


def test_unified_find_order_by_type():
    """Test finding orders by type"""
    # Create orders with different types
    orders = [
        create_test_order("type_test_1", "0xtype_addr", "type_provider", "pending"),
        create_test_order("type_test_2", "0xtype_addr", "type_provider", "pending"),
    ]

    orders[0]["type"] = "compute_order"
    orders[1]["type"] = "storage_order"

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Find by specific type
    filter_params = {
        "order_type": "compute_order",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    for order in found_orders:
        if order["_id"] == orders[0]["_id"]:
            assert order["type"] == "compute_order"


def test_unified_count_user_service_by_status():
    """Test counting user services by different statuses"""
    # Create services with different statuses
    statuses = ["active", "inactive", "suspended", "pending", "done"]
    services = []

    for i, status in enumerate(statuses):
        service = create_test_user_service(f"status_count_test_{i}", "0xstatus_count_addr", "status_provider", status)
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Count each status
    for status in statuses:
        filter_params = {
            "address": "0xstatus_count_addr",
            "status": status
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
        assert err is None

        count = int(result)
        assert count >= 1


def test_unified_count_order_by_status():
    """Test counting orders by different statuses"""
    # Create orders with different statuses
    statuses = ["pending", "paid", "cancelled", "refunded", "completed"]
    orders = []

    for i, status in enumerate(statuses):
        order = create_test_order(f"status_count_order_test_{i}", "0xstatus_count_order_addr", "status_order_provider", status)
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Count each status
    for status in statuses:
        filter_params = {
            "address": "0xstatus_count_order_addr",
            "statuses": [status]
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
        assert err is None

        count = int(result)
        assert count >= 1

def test_unified_find_order_by_status():
    """Test counting orders by different statuses"""
    # Create orders with different statuses
    statuses = ["pending", "paid", "cancelled", "refunded", "completed"]
    orders = []

    for i, status in enumerate(statuses):
        order = create_test_order(f"status_find_order_test_{i}", "0xstatus_count_order_addr", "status_order_provider", status)
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Count each status
    for status in statuses:
        filter_params = {
            "statuses": [status]
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
        found_orders = json.loads(result)
        assert len(found_orders) == 1
        for order in found_orders:
            if order["_id"] == orders[0]["_id"]:
                assert order["status"] == status

def test_unified_insert_many_user_service_large_batch():
    """Test inserting many user services in large batches"""
    # Create a larger batch
    services = []
    for i in range(10):
        service = create_test_user_service(f"large_batch_test_{i}", f"0xlarge_batch_addr_{i}", f"large_batch_provider_{i}", "active")
        services.append(service)

    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 10
    assert len(batch_result["errors"]) == 0


def test_unified_insert_many_order_large_batch():
    """Test inserting many orders in large batches"""
    # Create a larger batch
    orders = []
    for i in range(10):
        order = create_test_order(f"large_batch_order_test_{i}", f"0xlarge_batch_order_addr_{i}", f"large_batch_order_provider_{i}", "pending")
        orders.append(order)

    orders_json = json.dumps(orders)
    result, err = vcloudClient.execute("insert_many", str, "order", orders_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 10
    assert len(batch_result["errors"]) == 0


def test_unified_insert_many_user_service_with_duplicates():
    """Test inserting many user services with duplicate IDs"""
    # Create services with some duplicate IDs
    services = [
        create_test_user_service("duplicate_test_1", "0xduplicate_addr", "duplicate_provider", "active"),
        create_test_user_service("duplicate_test_2", "0xduplicate_addr", "duplicate_provider", "active"),
        create_test_user_service("duplicate_test_1", "0xduplicate_addr", "duplicate_provider", "active"),  # Duplicate ID
    ]

    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 2  # Only 2 should be created
    assert len(batch_result["errors"]) == 1  # 1 error for duplicate


def test_unified_insert_many_order_with_duplicates():
    """Test inserting many orders with duplicate IDs"""
    # Create orders with some duplicate IDs
    orders = [
        create_test_order("duplicate_order_test_1", "0xduplicate_order_addr", "duplicate_order_provider", "pending"),
        create_test_order("duplicate_order_test_2", "0xduplicate_order_addr", "duplicate_order_provider", "pending"),
        create_test_order("duplicate_order_test_1", "0xduplicate_order_addr", "duplicate_order_provider", "pending"),  # Duplicate ID
    ]

    orders_json = json.dumps(orders)
    result, err = vcloudClient.execute("insert_many", str, "order", orders_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 2  # Only 2 should be created
    assert len(batch_result["errors"]) == 1  # 1 error for duplicate


def test_unified_delete_user_service_by_address():
    """Test deleting user services by address"""
    # Create test services
    services = [
        create_test_user_service("delete_addr_test_1", "0xdelete_addr_test", "delete_addr_provider", "active"),
        create_test_user_service("delete_addr_test_2", "0xdelete_addr_test", "delete_addr_provider", "active"),
        create_test_user_service("delete_addr_test_3", "0xother_addr", "delete_addr_provider", "active"),
    ]

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Delete by address
    filter_params = {
        "address": "0xdelete_addr_test"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "user_service", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 services with matching address


def test_unified_delete_order_by_address():
    """Test deleting orders by address"""
    # Create test orders
    orders = [
        create_test_order("delete_addr_order_test_1", "0xdelete_addr_order_test", "delete_addr_order_provider", "pending"),
        create_test_order("delete_addr_order_test_2", "0xdelete_addr_order_test", "delete_addr_order_provider", "pending"),
        create_test_order("delete_addr_order_test_3", "0xother_addr_order", "delete_addr_order_provider", "pending"),
    ]

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Delete by address
    filter_params = {
        "address": "0xdelete_addr_order_test"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "order", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 2  # Should delete 2 orders with matching address


def test_unified_update_user_service_partial_fields():
    """Test updating user service with partial field changes"""
    # Create test service
    service = create_test_user_service("update_partial_test", "0xupdate_partial_addr", "update_partial_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Update only specific fields
    service["amount"] = 999.99
    service["status"] = "updated_status"
    service["serviceActivated"] = False
    updated_service_json = json.dumps(service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 999.99
    assert updated_service["status"] == "updated_status"
    assert updated_service["serviceActivated"] == False
    assert updated_service["provider"] == "update_partial_provider"  # Unchanged field


def test_unified_update_order_partial_fields():
    """Test updating order with partial field changes"""
    # Create test order
    order = create_test_order("update_partial_order_test", "0xupdate_partial_order_addr", "update_partial_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Update only specific fields
    order["amount"] = 1500.0
    order["amountPaid"] = 750.0
    order["status"] = "partially_paid"
    order["paidTS"] = int(time.time() * **********)
    updated_order_json = json.dumps(order)
    result, err = vcloudClient.execute("update", str, "order", updated_order_json)
    assert err is None

    # Verify the update
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["amount"] == 1500.0
    assert updated_order["amountPaid"] == 750.0
    assert updated_order["status"] == "partially_paid"
    assert updated_order["paidTS"] > 0
    assert updated_order["provider"] == "update_partial_order_provider"  # Unchanged field


def test_unified_get_user_service_multiple():
    """Test getting multiple user services by ID"""
    # Create multiple test services
    services = []
    for i in range(5):
        service = create_test_user_service(f"get_multiple_test_{i}", f"0xget_multiple_addr_{i}", f"get_multiple_provider_{i}", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Get each service individually
    for service in services:
        result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
        assert err is None
        retrieved_service = json.loads(result)
        assert retrieved_service["_id"] == service["_id"]
        assert retrieved_service["amount"] == service["amount"]
        assert retrieved_service["provider"] == service["provider"]


def test_unified_get_order_multiple():
    """Test getting multiple orders by ID"""
    # Create multiple test orders
    orders = []
    for i in range(5):
        order = create_test_order(f"get_multiple_order_test_{i}", f"0xget_multiple_order_addr_{i}", f"get_multiple_order_provider_{i}", "pending")
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Get each order individually
    for order in orders:
        result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
        assert err is None
        retrieved_order = json.loads(result)
        assert retrieved_order["_id"] == order["_id"]
        assert retrieved_order["amount"] == order["amount"]
        assert retrieved_order["provider"] == order["provider"]


def test_unified_find_user_service_with_sorting():
    """Test finding user services with sorting"""
    # Create services with different amounts for sorting
    services = []
    amounts = [100.0, 300.0, 200.0, 500.0, 150.0]

    for i, amount in enumerate(amounts):
        service = create_test_user_service(f"sort_test_{i}", "0xsort_addr", "sort_provider", "active")
        service["amount"] = amount
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Find with sorting (ascending by default)
    filter_params = {
        "address": "0xsort_addr",
        "limit": 10,
        "offset": 0,
        "sort_desc": False
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 5

    # Verify services are returned (sorting verification depends on implementation)
    service_ids = [s["_id"] for s in services]
    found_ids = [s["_id"] for s in found_services if s["_id"] in service_ids]
    assert len(found_ids) == 5


def test_unified_find_order_with_sorting():
    """Test finding orders with sorting"""
    # Create orders with different amounts for sorting
    orders = []
    amounts = [1000.0, 3000.0, 2000.0, 5000.0, 1500.0]

    for i, amount in enumerate(amounts):
        order = create_test_order(f"sort_order_test_{i}", "0xsort_order_addr", "sort_order_provider", "pending")
        order["amount"] = amount
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Find with sorting
    filter_params = {
        "address": "0xsort_order_addr",
        "limit": 10,
        "offset": 0,
        "sort_desc": True
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 5

    # Verify orders are returned
    order_ids = [o["_id"] for o in orders]
    found_ids = [o["_id"] for o in found_orders if o["_id"] in order_ids]
    assert len(found_ids) == 5


def test_unified_pagination_user_service_large_dataset():
    """Test pagination with large user service dataset"""
    # Create a large dataset
    services = []
    for i in range(25):
        service = create_test_user_service(f"large_pagination_test_{i}", "0xlarge_pagination_addr", "large_pagination_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test multiple pages
    all_found_ids = set()
    page_size = 5

    for page in range(5):  # 5 pages of 5 items each
        filter_params = {
            "address": "0xlarge_pagination_addr",
            "limit": page_size,
            "offset": page * page_size
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
        assert err is None

        page_services = json.loads(result)
        assert len(page_services) <= page_size

        # Collect IDs from this page
        page_ids = {s["_id"] for s in page_services if s["address"] == "0xlarge_pagination_addr"}

        # Ensure no overlap with previous pages
        assert len(all_found_ids.intersection(page_ids)) == 0
        all_found_ids.update(page_ids)

    # Should have found all 25 services
    assert len(all_found_ids) == 25


def test_unified_pagination_order_large_dataset():
    """Test pagination with large order dataset"""
    # Create a large dataset
    orders = []
    for i in range(25):
        order = create_test_order(f"large_pagination_order_test_{i}", "0xlarge_pagination_order_addr", "large_pagination_order_provider", "pending")
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Test multiple pages
    all_found_ids = set()
    page_size = 5

    for page in range(5):  # 5 pages of 5 items each
        filter_params = {
            "address": "0xlarge_pagination_order_addr",
            "limit": page_size,
            "offset": page * page_size
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
        assert err is None

        page_orders = json.loads(result)
        assert len(page_orders) <= page_size

        # Collect IDs from this page
        page_ids = {o["_id"] for o in page_orders if o["address"] == "0xlarge_pagination_order_addr"}

        # Ensure no overlap with previous pages
        assert len(all_found_ids.intersection(page_ids)) == 0
        all_found_ids.update(page_ids)

    # Should have found all 25 orders
    assert len(all_found_ids) == 25


def test_unified_error_handling_invalid_json():
    """Test error handling with various invalid JSON inputs"""
    # Test invalid JSON for find
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", "invalid_json{")
    assert err is not None

    # Test invalid JSON for insert
    result, err = vcloudClient.execute("insert", str, "user_service", "invalid_json{")
    assert err is not None

    # Test invalid JSON for count
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", "invalid_json{")
    assert err is not None

    # Test invalid JSON for update
    result, err = vcloudClient.execute("update", str, "user_service", "invalid_json{")
    assert err is not None

    # Test invalid JSON for delete
    result, err = vcloudClient.execute("delete", str, "user_service", "invalid_json{")
    assert err is not None


def test_unified_error_handling_empty_inputs():
    """Test error handling with empty inputs"""
    # Test empty table name
    result, err = vcloudClient.executeReadOnly("find", str, "", "{}")
    assert err is not None

    # Test empty JSON
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", "")
    assert err is not None

    # Test empty ID for get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", "")
    assert err is not None

    # Test empty ID for get order
    result, err = vcloudClient.executeReadOnly("get", str, "order", "")
    assert err is not None


def test_unified_data_validation_negative_amounts():
    """Test data validation with negative amounts"""
    # Test user service with negative amount
    service = create_test_user_service("negative_amount_test")
    service["amount"] = -100.0
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    # Note: This might succeed depending on validation rules
    # The test verifies the behavior is consistent

    # Test order with negative amount
    order = create_test_order("negative_amount_order_test")
    order["amount"] = -500.0
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    # Note: This might succeed depending on validation rules


def test_unified_data_validation_large_amounts():
    """Test data validation with very large amounts"""
    # Test user service with very large amount
    service = create_test_user_service("large_amount_test")
    service["amount"] = 999999999.99
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None  # Should handle large numbers

    # Test order with very large amount
    order = create_test_order("large_amount_order_test")
    order["amount"] = 999999999.99
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None  # Should handle large numbers


def test_unified_lifecycle_user_service_complete():
    """Test complete user service lifecycle with all operations"""
    # Create service
    service = create_test_user_service("lifecycle_complete_test", "0xlifecycle_complete_addr", "lifecycle_complete_provider", "pending")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Get service
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "pending"

    # Update service to active
    retrieved_service["status"] = "active"
    retrieved_service["serviceActivated"] = True
    retrieved_service["amount"] = 250.0
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Find service
    filter_params = {
        "address": "0xlifecycle_complete_addr",
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service_id for s in found_services)

    # Count services
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # Update service to done
    retrieved_service["status"] = "done"
    retrieved_service["serviceDoneTS"] = int(time.time() * **********)
    final_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", final_service_json)
    assert err is None

    # Delete service
    delete_filter = {"ids": [service_id]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "user_service", delete_filter_json)
    assert err is None
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1

    # Verify service is deleted
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is not None
    assert "not found" in str(err)


def test_unified_lifecycle_order_complete():
    """Test complete order lifecycle with all operations"""
    # Create order
    order = create_test_order("lifecycle_complete_order_test", "0xlifecycle_complete_order_addr", "lifecycle_complete_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    order_id = result

    # Get order
    result, err = vcloudClient.executeReadOnly("get", str, "order", order_id)
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["status"] == "pending"

    # Update order to processing
    retrieved_order["status"] = "processing"
    retrieved_order["lastPaymentTS"] = int(time.time() * **********)
    updated_order_json = json.dumps(retrieved_order)
    result, err = vcloudClient.execute("update", str, "order", updated_order_json)
    assert err is None

    # Find order
    filter_params = {
        "address": "0xlifecycle_complete_order_addr",
        "statuses": ["processing"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    assert any(o["_id"] == order_id for o in found_orders)

    # Count orders
    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # Update order to paid
    retrieved_order["status"] = "paid"
    retrieved_order["amountPaid"] = retrieved_order["amount"]
    retrieved_order["paidTS"] = int(time.time() * **********)
    final_order_json = json.dumps(retrieved_order)
    result, err = vcloudClient.execute("update", str, "order", final_order_json)
    assert err is None

    # Delete order
    delete_filter = {"_ids": [order_id]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "order", delete_filter_json)
    assert err is None
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1

    # Verify order is deleted
    result, err = vcloudClient.executeReadOnly("get", str, "order", order_id)
    assert err is not None
    assert "not found" in str(err)


def test_unified_complex_queries_user_service():
    """Test complex queries with multiple filters for user services"""
    # Create diverse test data
    services = [
        create_test_user_service("complex_1", "0xcomplex_addr1", "provider_alpha", "active"),
        create_test_user_service("complex_2", "0xcomplex_addr1", "provider_beta", "active"),
        create_test_user_service("complex_3", "0xcomplex_addr2", "provider_alpha", "inactive"),
        create_test_user_service("complex_4", "0xcomplex_addr2", "provider_beta", "suspended"),
        create_test_user_service("complex_5", "0xcomplex_addr1", "provider_alpha", "pending"),
    ]

    # Set different amounts and activation status
    services[0]["amount"] = 100.0
    services[0]["serviceActivated"] = True
    services[1]["amount"] = 200.0
    services[1]["serviceActivated"] = False
    services[2]["amount"] = 150.0
    services[2]["serviceActivated"] = True
    services[3]["amount"] = 300.0
    services[3]["serviceActivated"] = False
    services[4]["amount"] = 250.0
    services[4]["serviceActivated"] = True

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Complex query 1: address + provider + status
    filter_params = {
        "address": "0xcomplex_addr1",
        "provider": "provider_alpha",
        "status": "active",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == services[0]["_id"]:
            assert service["address"] == "0xcomplex_addr1"
            assert service["provider"] == "provider_alpha"
            assert service["status"] == "active"

    # Complex query 2: address + serviceActivated
    filter_params = {
        "address": "0xcomplex_addr1",
        "service_activated": True,
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 2  # services[0] and services[4]


def test_unified_complex_queries_order():
    """Test complex queries with multiple filters for orders"""
    # Create diverse test data
    orders = [
        create_test_order("complex_order_1", "0xcomplex_order_addr1", "order_provider_alpha", "pending"),
        create_test_order("complex_order_2", "0xcomplex_order_addr1", "order_provider_beta", "pending"),
        create_test_order("complex_order_3", "0xcomplex_order_addr2", "order_provider_alpha", "paid"),
        create_test_order("complex_order_4", "0xcomplex_order_addr2", "order_provider_beta", "cancelled"),
        create_test_order("complex_order_5", "0xcomplex_order_addr1", "order_provider_alpha", "completed"),
    ]

    # Set different amounts and recipients
    orders[0]["amount"] = 1000.0
    orders[0]["recipient"] = "0xrecipient_alpha"
    orders[1]["amount"] = 2000.0
    orders[1]["recipient"] = "0xrecipient_beta"
    orders[2]["amount"] = 1500.0
    orders[2]["recipient"] = "0xrecipient_alpha"
    orders[2]["amountPaid"] = 1500.0
    orders[3]["amount"] = 3000.0
    orders[3]["recipient"] = "0xrecipient_beta"
    orders[4]["amount"] = 2500.0
    orders[4]["recipient"] = "0xrecipient_alpha"
    orders[4]["amountPaid"] = 2500.0

    for order in orders:
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Complex query 1: address + provider + status
    filter_params = {
        "address": "0xcomplex_order_addr1",
        "provider": "order_provider_alpha",
        "statuses": ["pending", "completed"],
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    found_orders = json.loads(result)
    assert len(found_orders) >= 2  # orders[0] and orders[4]

    # Complex query 2: address + recipient
    filter_params = {
        "address": "0xcomplex_order_addr1",
        "recipient": "0xrecipient_alpha",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    found_orders = json.loads(result)
    assert len(found_orders) >= 2  # orders[0] and orders[4]


def test_unified_performance_large_insert_many():
    """Test performance with large batch inserts"""
    # Test large batch of user services
    large_services = []
    for i in range(50):
        service = create_test_user_service(f"perf_test_{i}", f"0xperf_addr_{i % 10}", f"perf_provider_{i % 5}", "active")
        large_services.append(service)

    services_json = json.dumps(large_services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 50
    assert len(batch_result["errors"]) == 0

    # Test large batch of orders
    large_orders = []
    for i in range(50):
        order = create_test_order(f"perf_order_test_{i}", f"0xperf_order_addr_{i % 10}", f"perf_order_provider_{i % 5}", "pending")
        large_orders.append(order)

    orders_json = json.dumps(large_orders)
    result, err = vcloudClient.execute("insert_many", str, "order", orders_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 50
    assert len(batch_result["errors"]) == 0


def test_unified_boundary_conditions():
    """Test boundary conditions and edge cases"""
    # Test with zero values
    service = create_test_user_service("boundary_test")
    service["amount"] = 0.0
    service["duration"] = 0
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Test with very small positive values
    order = create_test_order("boundary_order_test")
    order["amount"] = 0.01
    order["amountPaid"] = 0.001
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Test with empty arrays and objects
    service2 = create_test_user_service("boundary_test_2")
    service2["serviceOptions"] = {}
    service2_json = json.dumps(service2)
    result, err = vcloudClient.execute("insert", str, "user_service", service2_json)
    assert err is None

    order2 = create_test_order("boundary_order_test_2")
    order2["userServiceIDs"] = []
    order2["items"] = []
    order2_json = json.dumps(order2)
    result, err = vcloudClient.execute("insert", str, "order", order2_json)
    assert err is None


def test_unified_concurrent_operations():
    """Test concurrent-like operations (sequential but rapid)"""
    # Rapid sequential inserts
    services = []
    for i in range(20):
        service = create_test_user_service(f"concurrent_test_{i}", "0xconcurrent_addr", "concurrent_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Rapid sequential queries
    for i in range(10):
        filter_params = {
            "address": "0xconcurrent_addr",
            "limit": 5,
            "offset": i * 2
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
        assert err is None
        found_services = json.loads(result)
        assert len(found_services) <= 5

    # Rapid sequential updates
    for i, service in enumerate(services[:10]):
        service["amount"] = float(i * 100)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("update", str, "user_service", service_json)
        assert err is None


def test_unified_data_consistency():
    """Test data consistency across operations"""
    # Create initial data
    service = create_test_user_service("consistency_test", "0xconsistency_addr", "consistency_provider", "active")
    service["amount"] = 500.0
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Verify data through get
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["amount"] == 500.0
    assert retrieved_service["status"] == "active"

    # Verify data through find
    filter_params = {
        "ids": [service_id]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) == 1
    assert found_services[0]["amount"] == 500.0
    assert found_services[0]["status"] == "active"

    # Update and verify consistency
    retrieved_service["amount"] = 750.0
    retrieved_service["status"] = "updated"
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # Verify update through both get and find
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 750.0
    assert updated_service["status"] == "updated"

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) == 1
    assert found_services[0]["amount"] == 750.0
    assert found_services[0]["status"] == "updated"


def test_unified_special_characters_handling():
    """Test handling of special characters in data"""
    # Test with special characters in strings
    service = create_test_user_service("special_chars_test")
    service["provider"] = "provider-with-dashes_and_underscores"
    service["address"] = "0x123abc!@#$%^&*()"
    service["serviceOptions"] = {
        "special-key": "special-value",
        "unicode": "测试中文字符",
        "symbols": "!@#$%^&*()"
    }
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify special characters are preserved
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["provider"] == "provider-with-dashes_and_underscores"
    assert retrieved_service["serviceOptions"]["unicode"] == "测试中文字符"


def test_unified_timestamp_handling():
    """Test timestamp handling in various scenarios"""
    # Test with explicit timestamps
    current_time = int(time.time() * **********)

    service = create_test_user_service("timestamp_test")
    service["createdAt"] = current_time
    service["updatedAt"] = current_time
    service["serviceActivateTS"] = current_time - **********
    service["serviceRunningTS"] = current_time - 500000000
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify timestamps are preserved or auto-generated
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["createdAt"] > 0
    assert retrieved_service["updatedAt"] > 0

    # Test order timestamps
    order = create_test_order("timestamp_order_test")
    order["createdAt"] = current_time
    order["lastPaymentTS"] = current_time - 2000000000
    order["paidTS"] = current_time - **********
    order["filedTS"] = current_time - 500000000
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["createdAt"] > 0
    assert retrieved_order["lastPaymentTS"] == current_time - 2000000000


def test_unified_array_field_operations():
    """Test operations with array fields"""
    # Test order with multiple user service IDs and items
    order = create_test_order("array_test")
    order["userServiceIDs"] = ["service_1", "service_2", "service_3"]
    order["items"] = [
        {
            "userServiceID": "service_1",
            "duration": 3600,
            "amount": 100.0
        },
        {
            "userServiceID": "service_2",
            "duration": 7200,
            "amount": 200.0
        },
        {
            "userServiceID": "service_3",
            "duration": 1800,
            "amount": 50.0
        }
    ]
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Verify array fields are preserved
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert len(retrieved_order["userServiceIDs"]) == 3
    assert len(retrieved_order["items"]) == 3
    assert retrieved_order["items"][0]["userServiceID"] == "service_1"
    assert retrieved_order["items"][1]["amount"] == 200.0

    # Update array fields
    retrieved_order["userServiceIDs"].append("service_4")
    retrieved_order["items"].append({
        "userServiceID": "service_4",
        "duration": 5400,
        "amount": 150.0
    })
    updated_order_json = json.dumps(retrieved_order)
    result, err = vcloudClient.execute("update", str, "order", updated_order_json)
    assert err is None

    # Verify updated arrays
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    final_order = json.loads(result)
    assert len(final_order["userServiceIDs"]) == 4
    assert len(final_order["items"]) == 4
    assert final_order["items"][3]["userServiceID"] == "service_4"


def test_unified_boolean_field_operations():
    """Test operations with boolean fields"""
    # Test different boolean values
    services = [
        create_test_user_service("bool_test_1", "0xbool_addr", "bool_provider", "active"),
        create_test_user_service("bool_test_2", "0xbool_addr", "bool_provider", "active"),
    ]

    services[0]["serviceActivated"] = True
    services[1]["serviceActivated"] = False

    for service in services:
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Query by boolean field
    filter_params = {
        "address": "0xbool_addr",
        "service_activated": True
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == services[0]["_id"]:
            assert service["serviceActivated"] == True

    # Query by boolean field (false)
    filter_params["service_activated"] = False
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == services[1]["_id"]:
            assert service["serviceActivated"] == False


def test_unified_numeric_precision():
    """Test numeric precision handling"""
    # Test with high precision decimal values
    service = create_test_user_service("precision_test")
    service["amount"] = 123.456789
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    # Note: Precision may be limited by float64
    assert abs(retrieved_service["amount"] - 123.456789) < 0.000001

    # Test order with high precision
    order = create_test_order("precision_order_test")
    order["amount"] = 9876.543210
    order["amountPaid"] = 1234.567890
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert abs(retrieved_order["amount"] - 9876.543210) < 0.000001
    assert abs(retrieved_order["amountPaid"] - 1234.567890) < 0.000001


def test_unified_string_length_limits():
    """Test string length handling"""
    # Test with very long strings
    long_string = "a" * 128  # 1000 character string

    service = create_test_user_service("long_string_test")
    service["provider"] = long_string
    service["publicKey"] = "0x" + "f" * 128  # Long hex string
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert len(retrieved_service["provider"]) == 128
    assert retrieved_service["publicKey"] == "0x" + "f" * 128

    # Test order with long strings
    order = create_test_order("long_string_order_test")
    order["recipient"] = long_string
    order["publicKey"] = "0x" + "a" * 128
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert len(retrieved_order["recipient"]) == 128


def test_unified_bulk_write_user_service():
    """Test bulk write operations for user services"""
    # Test bulk write with mixed operations
    operations = [
        {
            "type": "insert",
            "data": create_test_user_service("bulk_insert_1", "0xbulk_addr", "bulk_provider", "active")
        },
        {
            "type": "insert",
            "data": create_test_user_service("bulk_insert_2", "0xbulk_addr", "bulk_provider", "active")
        }
    ]

    bulk_operations_json = json.dumps(operations)
    result, err = vcloudClient.execute("bulk_write", str, "user_service", bulk_operations_json)
    # Note: This test depends on bulk_write implementation
    # May need to be adjusted based on actual implementation


def test_unified_bulk_write_order():
    """Test bulk write operations for orders"""
    # Test bulk write with mixed operations
    operations = [
        {
            "type": "insert",
            "data": create_test_order("bulk_order_insert_1", "0xbulk_order_addr", "bulk_order_provider", "pending")
        },
        {
            "type": "insert",
            "data": create_test_order("bulk_order_insert_2", "0xbulk_order_addr", "bulk_order_provider", "pending")
        }
    ]

    bulk_operations_json = json.dumps(operations)
    result, err = vcloudClient.execute("bulk_write", str, "order", bulk_operations_json)
    # Note: This test depends on bulk_write implementation


def test_unified_find_user_service_empty_results():
    """Test finding user services with filters that return no results"""
    # Query with non-existent address
    filter_params = {
        "address": "0xnonexistent_address_12345",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 0


def test_unified_find_order_empty_results():
    """Test finding orders with filters that return no results"""
    # Query with non-existent address
    filter_params = {
        "address": "0xnonexistent_order_address_12345",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) == 0


def test_unified_count_user_service_zero_results():
    """Test counting user services with filters that return zero"""
    # Count with non-existent provider
    filter_params = {
        "provider": "nonexistent_provider_12345"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


def test_unified_count_order_zero_results():
    """Test counting orders with filters that return zero"""
    # Count with non-existent status
    filter_params = {
        "statuses": ["nonexistent_status_12345"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


def test_unified_insert_user_service_duplicate_error():
    """Test inserting duplicate user service IDs"""
    # Create first service
    service = create_test_user_service("duplicate_id_test")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Try to insert same ID again
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is not None
    assert "already exists" in str(err)


def test_unified_insert_order_duplicate_error():
    """Test inserting duplicate order IDs"""
    # Create first order
    order = create_test_order("duplicate_order_id_test")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Try to insert same ID again
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is not None
    assert "already exists" in str(err)


def test_unified_update_user_service_nonexistent():
    """Test updating non-existent user service"""
    # Try to update non-existent service
    service = create_test_user_service("nonexistent_update_test")
    service["_id"] = "definitely_nonexistent_id_12345"
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("update", str, "user_service", service_json)
    assert err is not None
    assert "not found" in str(err)


def test_unified_update_order_nonexistent():
    """Test updating non-existent order"""
    # Try to update non-existent order
    order = create_test_order("nonexistent_order_update_test")
    order["_id"] = "definitely_nonexistent_order_id_12345"
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("update", str, "order", order_json)
    assert err is not None
    assert "not found" in str(err)


def test_unified_delete_user_service_nonexistent():
    """Test deleting non-existent user service"""
    # Try to delete non-existent service
    filter_params = {
        "ids": ["definitely_nonexistent_id_12345"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 0


def test_unified_delete_order_nonexistent():
    """Test deleting non-existent order"""
    # Try to delete non-existent order
    filter_params = {
        "_ids": ["definitely_nonexistent_order_id_12345"]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "order", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 0


def test_unified_find_user_service_with_ids_filter():
    """Test finding user services using IDs filter"""
    # Create multiple services
    services = []
    for i in range(3):
        service = create_test_user_service(f"ids_filter_test_{i}", "0xids_filter_addr", "ids_filter_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Query using IDs filter
    filter_params = {
        "ids": [services[0]["_id"], services[2]["_id"]],  # Query for first and third
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 2
    found_ids = [s["_id"] for s in found_services]
    assert services[0]["_id"] in found_ids
    assert services[2]["_id"] in found_ids


def test_unified_find_order_with_ids_filter():
    """Test finding orders using IDs filter"""
    # Create multiple orders
    orders = []
    for i in range(3):
        order = create_test_order(f"ids_filter_order_test_{i}", "0xids_filter_order_addr", "ids_filter_order_provider", "pending")
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Query using IDs filter
    filter_params = {
        "_ids": [orders[0]["_id"], orders[2]["_id"]],  # Query for first and third
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 2
    found_ids = [o["_id"] for o in found_orders]
    assert orders[0]["_id"] in found_ids
    assert orders[2]["_id"] in found_ids


def test_unified_update_many_user_service():
    """Test update_many function for user services"""
    # Create test services
    services = []
    for i in range(3):
        service = create_test_user_service(f"update_many_test_{i}", "0xupdate_many_addr", "update_many_provider", "active")
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Update many services
    update_params = {
        "filter": {
            "address": "0xupdate_many_addr"
        },
        "update_datas": {
            "status": "updated_status",
            "amount": 999.0
        }
    }
    update_json = json.dumps(update_params)
    result, err = vcloudClient.execute("update_many", str, "user_service", update_json)
    # Note: This test depends on update_many implementation


def test_unified_update_many_order():
    """Test update_many function for orders"""
    # Create test orders
    orders = []
    for i in range(3):
        order = create_test_order(f"update_many_order_test_{i}", "0xupdate_many_order_addr", "update_many_order_provider", "pending")
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Update many orders
    update_params = {
        "filter": {
            "address": "0xupdate_many_order_addr"
        },
        "update_datas": {
            "status": "updated_status",
            "amount": 1999.0
        }
    }
    update_json = json.dumps(update_params)
    result, err = vcloudClient.execute("update_many", str, "order", update_json)
    # Note: This test depends on update_many implementation


def test_unified_find_user_service_with_limit_zero():
    """Test finding user services with limit 0"""
    # Create test service
    service = create_test_user_service("limit_zero_test", "0xlimit_zero_addr", "limit_zero_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Query with limit 0
    filter_params = {
        "address": "0xlimit_zero_addr",
        "limit": 0,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 0


def test_unified_find_order_with_limit_zero():
    """Test finding orders with limit 0"""
    # Create test order
    order = create_test_order("limit_zero_order_test", "0xlimit_zero_order_addr", "limit_zero_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Query with limit 0
    filter_params = {
        "address": "0xlimit_zero_order_addr",
        "limit": 0,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) == 0


def test_unified_find_user_service_with_high_offset():
    """Test finding user services with high offset"""
    # Create test service
    service = create_test_user_service("high_offset_test", "0xhigh_offset_addr", "high_offset_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Query with high offset
    filter_params = {
        "address": "0xhigh_offset_addr",
        "limit": 10,
        "offset": 1000  # Very high offset
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 0  # Should return empty due to high offset


def test_unified_find_order_with_high_offset():
    """Test finding orders with high offset"""
    # Create test order
    order = create_test_order("high_offset_order_test", "0xhigh_offset_order_addr", "high_offset_order_provider", "pending")
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Query with high offset
    filter_params = {
        "address": "0xhigh_offset_order_addr",
        "limit": 10,
        "offset": 1000  # Very high offset
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) == 0  # Should return empty due to high offset


def test_unified_insert_user_service_with_all_timestamps():
    """Test inserting user service with all timestamp fields set"""
    current_time = int(time.time() * **********)

    service = create_test_user_service("all_timestamps_test")
    service["createdAt"] = current_time
    service["updatedAt"] = current_time
    service["deletedAt"] = 0
    service["endAt"] = current_time + 3600000000000  # 1 hour later
    service["serviceActivateTS"] = current_time + 60000000000  # 1 minute later
    service["serviceRunningTS"] = current_time + 120000000000  # 2 minutes later
    service["serviceAbortTS"] = 0
    service["serviceDoneTS"] = 0
    service["serviceRefundTS"] = 0

    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify timestamps are preserved
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["createdAt"] > 0
    assert retrieved_service["endAt"] > retrieved_service["createdAt"]


def test_unified_insert_order_with_all_timestamps():
    """Test inserting order with all timestamp fields set"""
    current_time = int(time.time() * **********)

    order = create_test_order("all_timestamps_order_test")
    order["createdAt"] = current_time
    order["updatedAt"] = current_time
    order["deletedAt"] = 0
    order["lastPaymentTS"] = current_time - 3600000000000  # 1 hour ago
    order["paidTS"] = current_time - 1800000000000  # 30 minutes ago
    order["filedTS"] = current_time - 900000000000  # 15 minutes ago

    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Verify timestamps are preserved
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert retrieved_order["createdAt"] > 0
    assert retrieved_order["lastPaymentTS"] < retrieved_order["createdAt"]


def test_unified_find_user_service_by_duration():
    """Test finding user services by duration"""
    # Create services with different durations
    services = []
    durations = [3600, 7200, 1800, 10800]  # 1h, 2h, 30min, 3h

    for i, duration in enumerate(durations):
        service = create_test_user_service(f"duration_test_{i}", "0xduration_addr", "duration_provider", "active")
        service["duration"] = duration
        services.append(service)
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Find services with specific duration
    filter_params = {
        "address": "0xduration_addr",
        "duration": 7200,  # 2 hours
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == services[1]["_id"]:
            assert service["duration"] == 7200


def test_unified_find_order_by_amount_range():
    """Test finding orders by amount range (if supported)"""
    # Create orders with different amounts
    orders = []
    amounts = [100.0, 500.0, 1000.0, 2000.0]

    for i, amount in enumerate(amounts):
        order = create_test_order(f"amount_range_test_{i}", "0xamount_range_addr", "amount_range_provider", "pending")
        order["amount"] = amount
        orders.append(order)
        order_json = json.dumps(order)
        result, err = vcloudClient.execute("insert", str, "order", order_json)
        assert err is None

    # Find orders with specific amount
    filter_params = {
        "address": "0xamount_range_addr",
        "amount": 1000.0,
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    # Note: This test depends on whether amount filtering is supported
    # May need adjustment based on actual implementation


def test_unified_insert_user_service_with_complex_service_options():
    """Test inserting user service with complex serviceOptions"""
    service = create_test_user_service("complex_options_test")
    service["serviceOptions"] = {
        "cpu": "4",
        "memory": "8GB",
        "storage": "100GB",
    }

    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify complex object is preserved
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None


def test_unified_insert_order_with_complex_items():
    """Test inserting order with complex items array"""
    order = create_test_order("complex_items_test")
    order["items"] = [
        {
            "userServiceID": "service_compute_1",
            "duration": 3600,
            "amount": 100.0
        },
        {
            "userServiceID": "service_storage_1",
            "duration": 86400,  # 24 hours
            "amount": 50.0
        },
        {
            "userServiceID": "service_network_1",
            "duration": 7200,  # 2 hours
            "amount": 25.0
        }
    ]

    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Verify complex items array is preserved
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert len(retrieved_order["items"]) == 3
    assert retrieved_order["items"][0]["userServiceID"] == "service_compute_1"
    assert retrieved_order["items"][1]["duration"] == 86400
    assert retrieved_order["items"][2]["amount"] == 25.0


def test_unified_stress_test_rapid_operations():
    """Test rapid sequential operations for stress testing"""
    # Rapid inserts
    service_ids = []
    for i in range(20):
        service = create_test_user_service(f"stress_test_{i}", "0xstress_addr", "stress_provider", "active")
        service_ids.append(service["_id"])
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Rapid queries
    for i in range(10):
        filter_params = {
            "address": "0xstress_addr",
            "limit": 5,
            "offset": i
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
        assert err is None

    # Rapid gets
    for service_id in service_ids[:10]:
        result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
        assert err is None

    # Rapid deletes
    for service_id in service_ids[:5]:
        filter_params = {"ids": [service_id]}
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.execute("delete", str, "user_service", filter_json)
        assert err is None


def test_unified_memory_efficiency_large_objects():
    """Test memory efficiency with large objects"""
    # Create service with large data
    large_data = "x" * 10000  # 10KB string

    service = create_test_user_service("memory_test")
    service["serviceOptions"] = {
        "large_field_1": large_data,
        "large_field_2": large_data,
    }

    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify large object handling
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert len(retrieved_service["serviceOptions"]["large_field_1"]) == 10000


def test_unified_edge_case_empty_arrays():
    """Test edge cases with empty arrays"""
    # User service with empty arrays
    service = create_test_user_service("empty_arrays_test")
    service["serviceOptions"] = {}

    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Order with empty arrays
    order = create_test_order("empty_arrays_order_test")
    order["userServiceIDs"] = []
    order["items"] = []

    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None

    # Verify empty arrays are preserved
    result, err = vcloudClient.executeReadOnly("get", str, "order", order["_id"])
    assert err is None
    retrieved_order = json.loads(result)
    assert len(retrieved_order["userServiceIDs"]) == 0
    assert len(retrieved_order["items"]) == 0


def test_unified_edge_case_null_values():
    """Test edge cases with null/None values"""
    # Test with null string fields (empty strings)
    service = create_test_user_service("null_values_test")
    service["provider"] = ""
    service["publicKey"] = ""
    service["serviceID"] = ""

    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None

    # Verify null values are handled
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["provider"] == ""
    assert retrieved_service["publicKey"] == ""


def test_unified_cross_table_consistency():
    """Test consistency across user_service and order tables"""
    # Create user service
    service = create_test_user_service("consistency_cross_test", "0xcross_addr", "cross_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Create order referencing the service
    order = create_test_order("consistency_cross_order_test", "0xcross_addr", "cross_provider", "pending")
    order["userServiceIDs"] = [service_id]
    order["items"] = [{
        "userServiceID": service_id,
        "duration": 3600,
        "amount": 100.0
    }]
    order_json = json.dumps(order)
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    order_id = result

    # Verify both exist and are consistent
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)

    result, err = vcloudClient.executeReadOnly("get", str, "order", order_id)
    assert err is None
    retrieved_order = json.loads(result)

    assert retrieved_service["address"] == retrieved_order["address"]
    assert retrieved_service["provider"] == retrieved_order["provider"]
    assert service_id in retrieved_order["userServiceIDs"]


def test_unified_transaction_like_behavior():
    """Test transaction-like behavior with batch operations"""
    # Test insert_many with mixed success/failure
    services = [
        create_test_user_service("transaction_test_1", "0xtransaction_addr", "transaction_provider", "active"),
        create_test_user_service("transaction_test_2", "0xtransaction_addr", "transaction_provider", "active"),
        create_test_user_service("", "0xtransaction_addr", "transaction_provider", "active"),  # Invalid empty ID
    ]

    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 2  # Only valid ones created
    assert len(batch_result["errors"]) == 1  # One error for empty ID


def test_unified_data_integrity_after_updates():
    """Test data integrity after multiple updates"""
    # Create service
    service = create_test_user_service("integrity_test", "0xintegrity_addr", "integrity_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # Multiple updates
    for i in range(5):
        result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
        assert err is None
        current_service = json.loads(result)

        current_service["amount"] = float(i * 100)
        current_service["status"] = f"status_{i}"

        updated_service_json = json.dumps(current_service)
        result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
        assert err is None

    # Verify final state
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    final_service = json.loads(result)
    assert final_service["amount"] == 400.0
    assert final_service["status"] == "status_4"


def test_unified_performance_pagination_large_dataset():
    """Test pagination performance with large dataset"""
    # Create large dataset
    for i in range(100):
        service = create_test_user_service(f"perf_pagination_test_{i}", "0xperf_pagination_addr", "perf_pagination_provider", "active")
        service_json = json.dumps(service)
        result, err = vcloudClient.execute("insert", str, "user_service", service_json)
        assert err is None

    # Test pagination through large dataset
    total_found = 0
    page_size = 10
    page = 0

    while True:
        filter_params = {
            "address": "0xperf_pagination_addr",
            "limit": page_size,
            "offset": page * page_size
        }
        filter_json = json.dumps(filter_params)
        result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
        assert err is None

        page_services = json.loads(result)
        if len(page_services) == 0:
            break

        total_found += len(page_services)
        page += 1

        if page > 20:  # Safety break
            break

    assert total_found >= 100


def test_unified_final_comprehensive_test():
    """Final comprehensive test covering all 9 unified functions"""
    # Test all 9 unified interface functions in sequence

    # 1. insert - Create user service
    service = create_test_user_service("comprehensive_test", "0xcomprehensive_addr", "comprehensive_provider", "active")
    service_json = json.dumps(service)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    service_id = result

    # 2. get - Retrieve the service
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == service_id

    # 3. find - Find the service
    filter_params = {"address": "0xcomprehensive_addr"}
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1

    # 4. count - Count services
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # 5. insert_many - Create multiple services
    services = [
        create_test_user_service("comprehensive_test_2", "0xcomprehensive_addr", "comprehensive_provider", "active"),
        create_test_user_service("comprehensive_test_3", "0xcomprehensive_addr", "comprehensive_provider", "active"),
    ]
    services_json = json.dumps(services)
    result, err = vcloudClient.execute("insert_many", str, "user_service", services_json)
    assert err is None

    # 6. update - Update the original service
    retrieved_service["amount"] = 999.0
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # 7. update_many - Update multiple services (if implemented)
    # Note: This depends on implementation

    # 8. delete - Delete one service
    delete_filter = {"ids": [services[0]["_id"]]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "user_service", delete_filter_json)
    assert err is None

    # 9. delete_many - Delete remaining services
    delete_many_filter = {"address": "0xcomprehensive_addr"}
    delete_many_filter_json = json.dumps(delete_many_filter)
    result, err = vcloudClient.execute("delete_many", str, "user_service", delete_many_filter_json)
    assert err is None

    # Verify all services are deleted
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    final_count = int(result)
    assert final_count == 0


# ========== CLI VERSION TESTS ==========

def test_unified_insert_cli_version():
    """Test unified insert function for CLI versions"""
    cli_version = create_test_cli_version("unified_insert_1.0.0", "Unified insert test", "1.0.0")
    cli_version_json = json.dumps(cli_version)

    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
    assert err is None
    assert result == "unified_insert_1.0.0"


def test_unified_get_cli_version():
    """Test unified get function for CLI versions"""
    cli_version = create_test_cli_version("unified_get_1.0.0", "Unified get test", "1.0.0")
    cli_version_json = json.dumps(cli_version)

    # Insert CLI version first
    result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
    assert err is None
    version_id = result

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "cli_version", version_id)
    assert err is None
    retrieved_cli_version = json.loads(result)
    assert retrieved_cli_version["version"] == "unified_get_1.0.0"
    assert retrieved_cli_version["changeLog"] == "Unified get test"


def test_unified_find_cli_version():
    """Test unified find function for CLI versions"""
    # Create test CLI versions
    cli_versions = [
        create_test_cli_version("find_test_1.0.0", "Find test 1", "1.0.0"),
        create_test_cli_version("find_test_1.1.0", "Find test 2", "1.0.0"),
    ]

    for cli_version in cli_versions:
        cli_version_json = json.dumps(cli_version)
        result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
        assert err is None

    # Test unified find with empty filter
    filter_params = {
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "cli_version", filter_json)
    assert err is None

    found_cli_versions = json.loads(result)
    assert len(found_cli_versions) >= 2


def test_unified_count_cli_version():
    """Test unified count function for CLI versions"""
    # Create test CLI versions
    cli_versions = [
        create_test_cli_version("count_test_1.0.0", "Count test 1", "1.0.0"),
        create_test_cli_version("count_test_1.1.0", "Count test 2", "1.0.0"),
    ]

    for cli_version in cli_versions:
        cli_version_json = json.dumps(cli_version)
        result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
        assert err is None

    # Test unified count
    filter_params = {}
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "cli_version", filter_json)
    assert err is None

    count = int(result)
    assert count >= 2


def test_unified_update_cli_version():
    """Test unified update function for CLI versions"""
    cli_version = create_test_cli_version("update_test_1.0.0", "Original change log", "1.0.0")
    cli_version_json = json.dumps(cli_version)

    # Insert CLI version first
    result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
    assert err is None

    # Update CLI version
    cli_version["changeLog"] = "Updated change log"
    updated_json = json.dumps(cli_version)

    result, err = vcloudClient.execute("update", str, "cli_version", updated_json)
    assert err is None

    # Verify update
    result, err = vcloudClient.executeReadOnly("get", str, "cli_version", "update_test_1.0.0")
    assert err is None
    retrieved_cli_version = json.loads(result)
    assert retrieved_cli_version["changeLog"] == "Updated change log"


def test_unified_insert_many_cli_version():
    """Test unified insert_many function for CLI versions"""
    cli_versions = [
        create_test_cli_version("batch_1.0.0", "Batch test 1", "1.0.0"),
        create_test_cli_version("batch_1.1.0", "Batch test 2", "1.0.0"),
    ]
    cli_versions_json = json.dumps(cli_versions)

    # Test unified insert_many
    result, err = vcloudClient.execute("insert_many", str, "cli_version", cli_versions_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_unified_update_many_cli_version():
    """Test unified update_many function for CLI versions"""
    # Create test CLI versions
    cli_versions = [
        create_test_cli_version("update_many_1.0.0", "Original 1", "1.0.0"),
        create_test_cli_version("update_many_1.1.0", "Original 2", "1.0.0"),
    ]

    for cli_version in cli_versions:
        cli_version_json = json.dumps(cli_version)
        result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
        assert err is None

    # Test unified update_many
    update_params = {
        "filter": {
            "minimalSupported": "1.0.0"
        },
        "update_data": {
            "changeLog": "Batch updated change log"
        }
    }
    update_json = json.dumps(update_params)

    result, err = vcloudClient.execute("update_many", str, "cli_version", update_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["updated"] >= 2


def test_unified_delete_cli_version():
    """Test unified delete function for CLI versions"""
    cli_version = create_test_cli_version("delete_test_1.0.0", "Delete test", "1.0.0")
    cli_version_json = json.dumps(cli_version)

    # Insert CLI version first
    result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
    assert err is None

    # Test unified delete
    filter_params = {
        "version": "delete_test_1.0.0"
    }
    filter_json = json.dumps(filter_params)

    result, err = vcloudClient.execute("delete", str, "cli_version", filter_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_unified_delete_many_cli_version():
    """Test unified delete_many function for CLI versions"""
    # Create test CLI versions
    cli_versions = [
        create_test_cli_version("delete_many_1.0.0", "Delete many 1", "1.0.0"),
        create_test_cli_version("delete_many_1.1.0", "Delete many 2", "1.0.0"),
    ]

    for cli_version in cli_versions:
        cli_version_json = json.dumps(cli_version)
        result, err = vcloudClient.execute("insert", str, "cli_version", cli_version_json)
        assert err is None

    # Test unified delete_many
    filter_params = {
        "minimalSupported": "1.0.0"
    }
    filter_json = json.dumps(filter_params)

    result, err = vcloudClient.execute("delete_many", str, "cli_version", filter_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["deleted"] >= 2


def test_unified_bulk_write_cli_version():
    """Test unified bulk_write function for CLI versions"""
    # Create bulk write operations
    operations = [
        {
            "type": "insert",
            "data": [
                create_test_cli_version("bulk_1.0.0", "Bulk insert 1", "1.0.0"),
                create_test_cli_version("bulk_1.1.0", "Bulk insert 2", "1.0.0")
            ]
        },
        {
            "type": "update",
            "filter": {
                "version": "bulk_1.0.0"
            },
            "data": {
                "changeLog": "Bulk updated"
            }
        }
    ]
    operations_json = json.dumps(operations)

    # Test unified bulk_write
    result, err = vcloudClient.execute("bulk_write", str, "cli_version", operations_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] >= 2
    assert batch_result["updated"] >= 1


# ========== CURRENCY TESTS ==========

def test_unified_insert_currency():
    """Test unified insert function for currencies"""
    currency = create_test_currency("unified_insert_currency_test", "Bitcoin", "BTC", "0xbtc_contract")
    currency_json = json.dumps(currency)

    # Test unified insert
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None
    assert result == currency["_id"]


def test_unified_find_currency():
    """Test unified find function for currencies"""
    # Create test currency
    currency = create_test_currency("unified_find_currency_test", "Ethereum", "ETH", "0xeth_contract")
    currency_json = json.dumps(currency)
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None

    # Test unified find
    filter_params = {
        "nameOrId": "Ethereum",
        "symbolName": "ETH",
        "limit": 10,
        "offset": 0
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "currency", filter_json)
    assert err is None

    found_currencies = json.loads(result)
    assert len(found_currencies) >= 1
    assert any(c["_id"] == currency["_id"] for c in found_currencies)


def test_unified_get_currency():
    """Test unified get function for currencies"""
    # Create test currency
    currency = create_test_currency("get_unified_currency_test", "Litecoin", "LTC", "0xltc_contract")
    currency_json = json.dumps(currency)
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None

    # Test unified get
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency["_id"])
    assert err is None
    retrieved_currency = json.loads(result)
    assert retrieved_currency["_id"] == currency["_id"]
    assert retrieved_currency["nameOrId"] == currency["nameOrId"]
    assert retrieved_currency["symbolName"] == currency["symbolName"]


def test_unified_count_currency():
    """Test unified count function for currencies"""
    # Create test currencies
    currencies = [
        create_test_currency("count_currency_test_1", "Bitcoin Cash", "BCH", "0xbch_contract"),
        create_test_currency("count_currency_test_2", "Dogecoin", "DOGE", "0xdoge_contract"),
    ]

    for currency in currencies:
        currency_json = json.dumps(currency)
        result, err = vcloudClient.execute("insert", str, "currency", currency_json)
        assert err is None

    # Test unified count
    filter_params = {
        "contractType": "ERC20"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "currency", filter_json)
    assert err is None

    count = int(result)
    assert count >= 2


def test_unified_insert_many_currency():
    """Test unified insert_many function for currencies"""
    currencies = [
        create_test_currency("insert_many_currency_1", "Cardano", "ADA", "0xada_contract"),
        create_test_currency("insert_many_currency_2", "Polkadot", "DOT", "0xdot_contract"),
        create_test_currency("insert_many_currency_3", "Chainlink", "LINK", "0xlink_contract")
    ]
    currencies_json = json.dumps(currencies)

    # Test unified insert_many
    result, err = vcloudClient.execute("insert_many", str, "currency", currencies_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] == 3
    assert batch_result["errors"] == []


def test_unified_update_currency():
    """Test unified update function for currencies"""
    # Create test currency
    currency = create_test_currency("update_currency_test", "Solana", "SOL", "0xsol_contract")
    currency_json = json.dumps(currency)
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None

    # Test unified update
    update_params = {
        "filter": {
            "ids": [currency["_id"]]
        },
        "update_data": {
            "exchangeRate": 75000.0,
            "symbolName": "SOL_UPDATED"
        }
    }
    update_json = json.dumps(update_params)
    result, err = vcloudClient.execute("update", str, "currency", update_json)
    assert err is None

    # Verify update
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency["_id"])
    assert err is None
    updated_currency = json.loads(result)
    assert updated_currency["exchangeRate"] == 75000.0
    assert updated_currency["symbolName"] == "SOL_UPDATED"


def test_unified_update_many_currency():
    """Test unified update_many function for currencies"""
    # Create test currencies
    currencies = [
        create_test_currency("update_many_currency_1", "Polygon", "MATIC", "0xmatic_contract"),
        create_test_currency("update_many_currency_2", "Avalanche", "AVAX", "0xavax_contract")
    ]

    for currency in currencies:
        currency_json = json.dumps(currency)
        result, err = vcloudClient.execute("insert", str, "currency", currency_json)
        assert err is None

    # Test unified update_many
    update_params = {
        "filter": {
            "contractType": "ERC20"
        },
        "update_data": {
            "unit": 18
        }
    }
    update_json = json.dumps(update_params)
    result, err = vcloudClient.execute("update_many", str, "currency", update_json)
    assert err is None

    # Verify updates
    for currency in currencies:
        result, err = vcloudClient.executeReadOnly("get", str, "currency", currency["_id"])
        assert err is None
        updated_currency = json.loads(result)
        assert updated_currency["unit"] == 18


def test_unified_delete_currency():
    """Test unified delete function for currencies"""
    # Create test currency
    currency = create_test_currency("delete_currency_test", "Tether", "USDT", "0xusdt_contract")
    currency_json = json.dumps(currency)
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None

    # Test unified delete
    filter_params = {
        "ids": [currency["_id"]]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "currency", filter_json)
    assert err is None

    # Verify deletion
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency["_id"])
    assert err is not None  # Should fail because currency was deleted


def test_unified_delete_many_currency():
    """Test unified delete_many function for currencies"""
    # Create test currencies
    currencies = [
        create_test_currency("delete_many_currency_1", "USD Coin", "USDC", "0xusdc_contract"),
        create_test_currency("delete_many_currency_2", "Binance USD", "BUSD", "0xbusd_contract")
    ]

    for currency in currencies:
        currency_json = json.dumps(currency)
        result, err = vcloudClient.execute("insert", str, "currency", currency_json)
        assert err is None

    # Test unified delete_many
    filter_params = {
        "contractType": "ERC20"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete_many", str, "currency", filter_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["deleted"] >= 2

    # Verify deletion
    for currency in currencies:
        result, err = vcloudClient.executeReadOnly("get", str, "currency", currency["_id"])
        assert err is not None  # Should fail because currencies were deleted


def test_unified_bulk_write_currency():
    """Test unified bulk_write function for currencies"""
    # Create bulk write operations
    operations = [
        {
            "type": "insert",
            "data": [
                create_test_currency("bulk_currency_1", "Cosmos", "ATOM", "0xatom_contract"),
                create_test_currency("bulk_currency_2", "Algorand", "ALGO", "0xalgo_contract")
            ]
        },
        {
            "type": "update",
            "filter": {
                "symbolName": "ATOM"
            },
            "data": {
                "exchangeRate": 12.5
            }
        }
    ]
    operations_json = json.dumps(operations)

    # Test unified bulk_write
    result, err = vcloudClient.execute("bulk_write", str, "currency", operations_json)
    assert err is None

    batch_result = json.loads(result)
    assert batch_result["created"] >= 2
    assert batch_result["updated"] >= 1


def test_unified_lifecycle_currency():
    """Test complete lifecycle using unified interface for currencies"""
    # Create currency using unified interface
    currency = create_test_currency("lifecycle_currency_test", "Stellar", "XLM", "0xxlm_contract")
    currency_json = json.dumps(currency)
    result, err = vcloudClient.execute("insert", str, "currency", currency_json)
    assert err is None
    currency_id = result

    # Get currency using unified interface
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency_id)
    assert err is None
    retrieved_currency = json.loads(result)
    assert retrieved_currency["symbolName"] == "XLM"

    # Update currency using unified interface
    update_params = {
        "filter": {
            "ids": [currency_id]
        },
        "update_data": {
            "exchangeRate": 0.25
        }
    }
    update_json = json.dumps(update_params)
    result, err = vcloudClient.execute("update", str, "currency", update_json)
    assert err is None

    # Verify update
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency_id)
    assert err is None
    updated_currency = json.loads(result)
    assert updated_currency["exchangeRate"] == 0.25

    # Find currency using unified interface
    filter_params = {
        "symbolName": "XLM"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "currency", filter_json)
    assert err is None
    found_currencies = json.loads(result)
    assert len(found_currencies) >= 1
    assert any(c["_id"] == currency_id for c in found_currencies)

    # Count currencies using unified interface
    filter_params = {
        "contractType": "ERC20"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("count", str, "currency", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # Delete currency using unified interface
    filter_params = {
        "ids": [currency_id]
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.execute("delete", str, "currency", filter_json)
    assert err is None

    # Verify deletion
    result, err = vcloudClient.executeReadOnly("get", str, "currency", currency_id)
    assert err is not None  # Should fail because currency was deleted
